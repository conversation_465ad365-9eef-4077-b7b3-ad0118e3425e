package net.minecraft.test;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import net.minecraft.network.PacketBuffer;

/**
 * Simple test class to verify that PacketBuffer can handle direct buffers
 * without throwing UnsupportedOperationException
 */
public class PacketBufferDirectBufferTest {
    
    public static void main(String[] args) {
        System.out.println("Testing PacketBuffer with direct buffers...");
        
        try {
            testDirectBufferHandling();
            testStringReadingWithDirectBuffer();
            testHeapBufferHandling();
            testStringReadingWithHeapBuffer();
            
            System.out.println("✅ All tests passed! Direct buffer handling is working correctly.");
        } catch (Exception e) {
            System.err.println("❌ Test failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    private static void testDirectBufferHandling() {
        System.out.println("Testing direct buffer array() and arrayOffset() methods...");
        
        // Test with direct buffer
        ByteBuf directBuf = Unpooled.directBuffer(1024);
        directBuf.writeBytes("Hello World".getBytes());
        
        PacketBuffer packetBuffer = new PacketBuffer(directBuf);
        
        // Test array() method with direct buffer
        byte[] array = packetBuffer.array();
        if (array == null) {
            throw new RuntimeException("Array should not be null");
        }
        if (array.length == 0) {
            throw new RuntimeException("Array should contain data");
        }
        
        // Test arrayOffset() method with direct buffer
        int offset = packetBuffer.arrayOffset();
        if (offset < 0) {
            throw new RuntimeException("Array offset should be >= 0");
        }
        
        directBuf.release();
        System.out.println("✓ Direct buffer array methods work correctly");
    }
    
    private static void testHeapBufferHandling() {
        System.out.println("Testing heap buffer array() method...");
        
        // Test with heap buffer
        ByteBuf heapBuf = Unpooled.buffer(1024);
        heapBuf.writeBytes("Hello World".getBytes());
        
        PacketBuffer packetBuffer = new PacketBuffer(heapBuf);
        
        // Test array() method with heap buffer
        byte[] array = packetBuffer.array();
        if (array == null) {
            throw new RuntimeException("Array should not be null");
        }
        if (array.length == 0) {
            throw new RuntimeException("Array should contain data");
        }
        
        heapBuf.release();
        System.out.println("✓ Heap buffer array methods work correctly");
    }
    
    private static void testStringReadingWithDirectBuffer() {
        System.out.println("Testing string reading with direct buffer...");
        
        // Test string reading with direct buffer
        ByteBuf directBuf = Unpooled.directBuffer(1024);
        PacketBuffer packetBuffer = new PacketBuffer(directBuf);
        
        // Write a string
        String testString = "Test String for Direct Buffer";
        packetBuffer.writeString(testString);
        
        // Read the string back
        String readString = packetBuffer.readStringFromBuffer(100);
        if (!testString.equals(readString)) {
            throw new RuntimeException("String should match. Expected: " + testString + ", Got: " + readString);
        }
        
        directBuf.release();
        System.out.println("✓ String reading with direct buffer works correctly");
    }
    
    private static void testStringReadingWithHeapBuffer() {
        System.out.println("Testing string reading with heap buffer...");
        
        // Test string reading with heap buffer
        ByteBuf heapBuf = Unpooled.buffer(1024);
        PacketBuffer packetBuffer = new PacketBuffer(heapBuf);
        
        // Write a string
        String testString = "Test String for Heap Buffer";
        packetBuffer.writeString(testString);
        
        // Read the string back
        String readString = packetBuffer.readStringFromBuffer(100);
        if (!testString.equals(readString)) {
            throw new RuntimeException("String should match. Expected: " + testString + ", Got: " + readString);
        }
        
        heapBuf.release();
        System.out.println("✓ String reading with heap buffer works correctly");
    }
}

Manifest-Version: 1.0
Main-Class: 
Class-Path: jspecify-1.0.0.jar netty-transport-native-epoll-4.2.1.Final-
 linux-x86_64.jar fastutil-8.5.15.jar netty-transport-native-io_uring-4.
 2.1.Final-linux-aarch_64.jar gson-2.13.1.jar netty-codec-classes-quic-4
 .2.1.Final.jar libraryjavasound-20101123.jar netty-buffer-4.2.1.Final.j
 ar netty-codec-native-quic-4.2.1.Final-linux-x86_64.jar netty-codec-nat
 ive-quic-4.2.1.Final-osx-x86_64.jar codecjorbis-20101023.jar netty-hand
 ler-4.2.1.Final.jar netty-transport-native-unix-common-4.2.1.Final.jar 
 codecwav-20101023.jar openauth-1.1.6.jar netty-common-4.2.1.Final.jar n
 etty-codec-native-quic-4.2.1.Final-windows-x86_64.jar lwjgl_util-2.9.3.
 jar netty-codec-xml-4.2.1.Final.jar netty-transport-native-kqueue-4.2.1
 .Final-osx-x86_64.jar commons-text-1.13.0.jar netty-transport-udt-4.2.1
 .Final.jar netty-all-4.2.1.Final.jar authlib-1.5.21.jar j2objc-annotati
 ons-3.0.0.jar jsr305-2.0.1.jar jinput-2.0.10.jar soundsystem-20120107.j
 ar listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar log4j
 -api-2.24.3.jar netty-resolver-4.2.1.Final.jar netty-codec-marshalling-
 4.2.1.Final.jar lwjgl-2.9.3.jar commons-compress-1.27.1.jar netty-resol
 ver-dns-native-macos-4.2.1.Final-osx-x86_64.jar netty-resolver-dns-clas
 ses-macos-4.2.1.Final.jar netty-codec-haproxy-4.2.1.Final.jar kotlin-st
 dlib-common-1.8.20.jar lwjgl-platform-2.9.3-natives-osx.jar netty-codec
 -redis-4.2.1.Final.jar lwjgl-platform-2.9.3-natives-linux.jar error_pro
 ne_annotations-2.38.0.jar netty-codec-native-quic-4.2.1.Final-linux-aar
 ch_64.jar netty-transport-native-io_uring-4.2.1.Final-linux-riscv64.jar
  netty-transport-4.2.1.Final.jar netty-codec-stomp-4.2.1.Final.jar comm
 ons-codec-1.18.0.jar netty-codec-dns-4.2.1.Final.jar kotlin-stdlib-jdk7
 -1.8.20.jar netty-resolver-dns-native-macos-4.2.1.Final-osx-aarch_64.ja
 r lwjgl-platform-2.9.3-natives-windows.jar commons-lang3-3.17.0.jar ann
 otations-13.0.jar netty-transport-native-kqueue-4.2.1.Final-osx-aarch_6
 4.jar netty-codec-protobuf-4.2.1.Final.jar netty-codec-compression-4.2.
 1.Final.jar netty-transport-sctp-4.2.1.Final.jar netty-codec-http2-4.2.
 1.Final.jar netty-transport-rxtx-4.2.1.Final.jar failureaccess-1.0.3.ja
 r netty-codec-4.2.1.Final.jar netty-codec-smtp-4.2.1.Final.jar netty-tr
 ansport-classes-kqueue-4.2.1.Final.jar netty-codec-http-4.2.1.Final.jar
  netty-transport-native-epoll-4.2.1.Final-linux-riscv64.jar netty-codec
 -memcache-4.2.1.Final.jar netty-transport-native-io_uring-4.2.1.Final-l
 inux-x86_64.jar kotlin-stdlib-jdk8-1.8.20.jar kotlin-stdlib-1.8.20.jar 
 librarylwjglopenal-20100824.jar netty-handler-ssl-ocsp-4.2.1.Final.jar 
 log4j-core-2.24.3.jar joml-1.10.8.jar netty-resolver-dns-4.2.1.Final.ja
 r guava-33.4.8-jre.jar netty-transport-classes-io_uring-4.2.1.Final.jar
  jcommander-2.0.jar commons-io-2.19.0.jar netty-codec-base-4.2.1.Final.
 jar netty-transport-classes-epoll-4.2.1.Final.jar netty-codec-mqtt-4.2.
 1.Final.jar icu4j-77.1.jar netty-codec-native-quic-4.2.1.Final-osx-aarc
 h_64.jar netty-codec-socks-4.2.1.Final.jar netty-handler-proxy-4.2.1.Fi
 nal.jar netty-transport-native-epoll-4.2.1.Final-linux-aarch_64.jar


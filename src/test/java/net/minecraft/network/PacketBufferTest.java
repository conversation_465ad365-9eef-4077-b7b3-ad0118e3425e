package net.minecraft.network;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.junit.Test;
import static org.junit.Assert.*;

public class PacketBufferTest {

    @Test
    public void testDirectBufferHandling() {
        // Test with direct buffer
        ByteBuf directBuf = Unpooled.directBuffer(1024);
        directBuf.writeBytes("Hello World".getBytes());
        
        PacketBuffer packetBuffer = new PacketBuffer(directBuf);
        
        // Test array() method with direct buffer
        try {
            byte[] array = packetBuffer.array();
            assertNotNull("Array should not be null", array);
            assertTrue("Array should contain data", array.length > 0);
        } catch (UnsupportedOperationException e) {
            fail("array() method should handle direct buffers: " + e.getMessage());
        }
        
        // Test arrayOffset() method with direct buffer
        try {
            int offset = packetBuffer.arrayOffset();
            assertTrue("Array offset should be >= 0", offset >= 0);
        } catch (UnsupportedOperationException e) {
            fail("arrayOffset() method should handle direct buffers: " + e.getMessage());
        }
        
        directBuf.release();
    }

    @Test
    public void testHeapBufferHandling() {
        // Test with heap buffer
        ByteBuf heapBuf = Unpooled.buffer(1024);
        heapBuf.writeBytes("Hello World".getBytes());
        
        PacketBuffer packetBuffer = new PacketBuffer(heapBuf);
        
        // Test array() method with heap buffer
        try {
            byte[] array = packetBuffer.array();
            assertNotNull("Array should not be null", array);
            assertTrue("Array should contain data", array.length > 0);
        } catch (UnsupportedOperationException e) {
            fail("array() method should handle heap buffers: " + e.getMessage());
        }
        
        heapBuf.release();
    }

    @Test
    public void testStringReadingWithDirectBuffer() {
        // Test string reading with direct buffer
        ByteBuf directBuf = Unpooled.directBuffer(1024);
        PacketBuffer packetBuffer = new PacketBuffer(directBuf);
        
        // Write a string
        String testString = "Test String for Direct Buffer";
        packetBuffer.writeString(testString);
        
        // Read the string back
        try {
            String readString = packetBuffer.readStringFromBuffer(100);
            assertEquals("String should match", testString, readString);
        } catch (UnsupportedOperationException e) {
            fail("String reading should work with direct buffers: " + e.getMessage());
        }
        
        directBuf.release();
    }

    @Test
    public void testStringReadingWithHeapBuffer() {
        // Test string reading with heap buffer
        ByteBuf heapBuf = Unpooled.buffer(1024);
        PacketBuffer packetBuffer = new PacketBuffer(heapBuf);
        
        // Write a string
        String testString = "Test String for Heap Buffer";
        packetBuffer.writeString(testString);
        
        // Read the string back
        try {
            String readString = packetBuffer.readStringFromBuffer(100);
            assertEquals("String should match", testString, readString);
        } catch (UnsupportedOperationException e) {
            fail("String reading should work with heap buffers: " + e.getMessage());
        }
        
        heapBuf.release();
    }
}
